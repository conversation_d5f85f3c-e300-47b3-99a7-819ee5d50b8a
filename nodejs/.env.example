# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=8000
HOST=0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://dev:devpass@localhost:5432/multitenancy_hub

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=30m

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# External API Keys (for future integrations)
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=
STRIPE_SECRET_KEY=
STRIPE_PUBLISHABLE_KEY=

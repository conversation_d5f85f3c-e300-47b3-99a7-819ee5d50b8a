version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: multitenancy-postgres
    environment:
      POSTGRES_DB: multitenancy_hub
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: devpass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - multitenancy-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev -d multitenancy_hub"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Node.js API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: multitenancy-api
    environment:
      NODE_ENV: production
      PORT: 8000
      DATABASE_URL: **************************************/multitenancy_hub
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 30m
      FRONTEND_URL: http://localhost:3000
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    networks:
      - multitenancy-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis (for future caching/sessions)
  redis:
    image: redis:7-alpine
    container_name: multitenancy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - multitenancy-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Nginx (for future load balancing/reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: multitenancy-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - multitenancy-network
    depends_on:
      - api
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  multitenancy-network:
    driver: bridge

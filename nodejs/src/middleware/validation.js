/**
 * Middleware to validate request data using Joi schemas
 * @param {Object} schema - Joi validation schema
 * @param {string} property - Request property to validate ('body', 'params', 'query')
 * @returns {Function} - Express middleware function
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false, // Return all validation errors
      allowUnknown: false, // Don't allow unknown fields
      stripUnknown: true, // Remove unknown fields
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        error: 'Dados de entrada inválidos',
        details
      });
    }

    // Replace the request property with the validated and sanitized value
    req[property] = value;
    next();
  };
}

/**
 * Middleware to validate request body
 * @param {Object} schema - Joi validation schema
 * @returns {Function} - Express middleware function
 */
function validateBody(schema) {
  return validate(schema, 'body');
}

/**
 * Middleware to validate request parameters
 * @param {Object} schema - Joi validation schema
 * @returns {Function} - Express middleware function
 */
function validateParams(schema) {
  return validate(schema, 'params');
}

/**
 * Middleware to validate request query parameters
 * @param {Object} schema - Joi validation schema
 * @returns {Function} - Express middleware function
 */
function validateQuery(schema) {
  return validate(schema, 'query');
}

module.exports = {
  validate,
  validateBody,
  validateParams,
  validateQuery,
};

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { Admin, Company } = require('../models');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30m';

/**
 * Hash a password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * Verify a password against its hash
 * @param {string} password - Plain text password
 * @param {string} hashedPassword - Hashed password
 * @returns {Promise<boolean>} - True if password matches
 */
async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * Create a JWT access token
 * @param {Object} payload - Token payload
 * @param {string} expiresIn - Token expiration time
 * @returns {string} - JWT token
 */
function createAccessToken(payload, expiresIn = JWT_EXPIRES_IN) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
}

/**
 * Verify a JWT token
 * @param {string} token - JWT token
 * @returns {Object} - Decoded token payload
 * @throws {Error} - If token is invalid
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    throw new Error('Token inválido');
  }
}

/**
 * Authenticate admin user
 * @param {string} username - Admin username
 * @param {string} password - Admin password
 * @returns {Promise<Admin|null>} - Admin object if authenticated, null otherwise
 */
async function authenticateAdmin(username, password) {
  try {
    const admin = await Admin.findOne({
      where: { username },
      include: [{
        model: Company,
        as: 'company'
      }]
    });

    if (!admin) {
      return null;
    }

    const isValidPassword = await verifyPassword(password, admin.password_hash);
    if (!isValidPassword) {
      return null;
    }

    return admin;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

/**
 * Get admin by ID
 * @param {number} adminId - Admin ID
 * @returns {Promise<Admin|null>} - Admin object if found, null otherwise
 */
async function getAdminById(adminId) {
  try {
    return await Admin.findByPk(adminId, {
      include: [{
        model: Company,
        as: 'company'
      }]
    });
  } catch (error) {
    console.error('Error fetching admin:', error);
    return null;
  }
}

module.exports = {
  hashPassword,
  verifyPassword,
  createAccessToken,
  verifyToken,
  authenticateAdmin,
  getAdminById,
};

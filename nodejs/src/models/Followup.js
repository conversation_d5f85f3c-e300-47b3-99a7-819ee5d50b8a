const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Followup = sequelize.define('Followup', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    company_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id',
      },
    },
    lead_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'leads',
        key: 'id',
      },
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    followup_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING(50),
      defaultValue: 'pending',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'followups',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });

  Followup.associate = (models) => {
    Followup.belongsTo(models.Company, {
      foreignKey: 'company_id',
      as: 'company',
    });
    Followup.belongsTo(models.Lead, {
      foreignKey: 'lead_id',
      as: 'lead',
    });
  };

  return Followup;
};

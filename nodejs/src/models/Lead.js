const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Lead = sequelize.define('Lead', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    company_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
    },
    social_media: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    source: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING(50),
      defaultValue: 'new',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'leads',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });

  Lead.associate = (models) => {
    Lead.belongsTo(models.Company, {
      foreignKey: 'company_id',
      as: 'company',
    });
    Lead.hasMany(models.Sale, {
      foreignKey: 'lead_id',
      as: 'sales',
    });
    Lead.hasMany(models.Followup, {
      foreignKey: 'lead_id',
      as: 'followups',
    });
    Lead.hasMany(models.Preorder, {
      foreignKey: 'lead_id',
      as: 'preorders',
    });
  };

  return Lead;
};

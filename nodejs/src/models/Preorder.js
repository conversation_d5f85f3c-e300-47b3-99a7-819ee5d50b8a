const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Preorder = sequelize.define('Preorder', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    company_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id',
      },
    },
    lead_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'leads',
        key: 'id',
      },
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'products',
        key: 'id',
      },
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    preorder_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    expected_delivery: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING(50),
      defaultValue: 'pending',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'preorders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });

  Preorder.associate = (models) => {
    Preorder.belongsTo(models.Company, {
      foreignKey: 'company_id',
      as: 'company',
    });
    Preorder.belongsTo(models.Lead, {
      foreignKey: 'lead_id',
      as: 'lead',
    });
    Preorder.belongsTo(models.Product, {
      foreignKey: 'product_id',
      as: 'product',
    });
  };

  return Preorder;
};

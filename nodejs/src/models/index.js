const { sequelize } = require('../config/database');

// Import all models
const Company = require('./Company');
const Admin = require('./Admin');
const TeamMember = require('./TeamMember');
const Product = require('./Product');
const Lead = require('./Lead');
const Sale = require('./Sale');
const Goal = require('./Goal');
const Followup = require('./Followup');
const Preorder = require('./Preorder');
const AuditLog = require('./AuditLog');

// Initialize models
const models = {
  Company: Company(sequelize),
  Admin: Admin(sequelize),
  TeamMember: TeamMember(sequelize),
  Product: Product(sequelize),
  Lead: Lead(sequelize),
  Sale: Sale(sequelize),
  Goal: Goal(sequelize),
  Followup: Followup(sequelize),
  Preorder: Preorder(sequelize),
  AuditLog: AuditLog(sequelize),
};

// Define associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

module.exports = {
  sequelize,
  ...models,
};

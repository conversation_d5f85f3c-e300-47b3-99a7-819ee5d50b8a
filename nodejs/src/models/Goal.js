const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Goal = sequelize.define('Goal', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    company_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id',
      },
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    target_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    current_value: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    goal_type: {
      type: DataTypes.STRING(50),
      allowNull: false, // 'sales', 'leads', 'revenue'
    },
    period_type: {
      type: DataTypes.STRING(20),
      allowNull: false, // 'monthly', 'quarterly', 'yearly'
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'goals',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });

  Goal.associate = (models) => {
    Goal.belongsTo(models.Company, {
      foreignKey: 'company_id',
      as: 'company',
    });
  };

  return Goal;
};

const express = require('express');
const { Lead } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validateBody, validateParams } = require('../middleware/validation');
const { createLeadSchema, updateLeadSchema, leadIdSchema } = require('../validators/leads');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * GET /leads
 * Get all leads for the authenticated admin's company
 */
router.get('/', async (req, res, next) => {
  try {
    const leads = await Lead.findAll({
      where: { company_id: req.companyId },
      order: [['created_at', 'DESC']]
    });

    res.json(leads);
  } catch (error) {
    logger.error(`Error fetching leads for company ${req.companyId}: ${error.message}`);
    next(error);
  }
});

/**
 * POST /leads
 * Create a new lead
 */
router.post('/', validateBody(createLeadSchema), async (req, res, next) => {
  try {
    const leadData = {
      ...req.body,
      company_id: req.companyId
    };

    const lead = await Lead.create(leadData);

    logger.info(`Lead created: ${lead.id} by admin ${req.admin.username}`);
    res.status(201).json(lead);
  } catch (error) {
    logger.error(`Error creating lead for company ${req.companyId}: ${error.message}`);
    next(error);
  }
});

/**
 * GET /leads/:lead_id
 * Get a specific lead by ID
 */
router.get('/:lead_id', validateParams(leadIdSchema), async (req, res, next) => {
  try {
    const { lead_id } = req.params;

    const lead = await Lead.findOne({
      where: {
        id: lead_id,
        company_id: req.companyId
      }
    });

    if (!lead) {
      return res.status(404).json({
        error: 'Lead não encontrado',
        code: 'LEAD_NOT_FOUND'
      });
    }

    res.json(lead);
  } catch (error) {
    logger.error(`Error fetching lead ${req.params.lead_id}: ${error.message}`);
    next(error);
  }
});

/**
 * PUT /leads/:lead_id
 * Update a specific lead
 */
router.put('/:lead_id', 
  validateParams(leadIdSchema), 
  validateBody(updateLeadSchema), 
  async (req, res, next) => {
    try {
      const { lead_id } = req.params;

      const lead = await Lead.findOne({
        where: {
          id: lead_id,
          company_id: req.companyId
        }
      });

      if (!lead) {
        return res.status(404).json({
          error: 'Lead não encontrado',
          code: 'LEAD_NOT_FOUND'
        });
      }

      // Update lead with provided data
      await lead.update(req.body);

      logger.info(`Lead updated: ${lead.id} by admin ${req.admin.username}`);
      res.json(lead);
    } catch (error) {
      logger.error(`Error updating lead ${req.params.lead_id}: ${error.message}`);
      next(error);
    }
  }
);

/**
 * DELETE /leads/:lead_id
 * Delete a specific lead
 */
router.delete('/:lead_id', validateParams(leadIdSchema), async (req, res, next) => {
  try {
    const { lead_id } = req.params;

    const lead = await Lead.findOne({
      where: {
        id: lead_id,
        company_id: req.companyId
      }
    });

    if (!lead) {
      return res.status(404).json({
        error: 'Lead não encontrado',
        code: 'LEAD_NOT_FOUND'
      });
    }

    await lead.destroy();

    logger.info(`Lead deleted: ${lead_id} by admin ${req.admin.username}`);
    res.json({
      message: 'Lead deletado com sucesso'
    });
  } catch (error) {
    logger.error(`Error deleting lead ${req.params.lead_id}: ${error.message}`);
    next(error);
  }
});

module.exports = router;

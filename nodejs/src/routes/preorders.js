const express = require('express');
const { Preorder, Lead, Product } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

router.get('/', async (req, res, next) => {
  try {
    const preorders = await Preorder.findAll({
      where: { company_id: req.companyId },
      include: [
        { model: Lead, as: 'lead', attributes: ['id', 'name'] },
        { model: Product, as: 'product', attributes: ['id', 'name'] }
      ],
      order: [['preorder_date', 'DESC']]
    });
    res.json(preorders);
  } catch (error) {
    next(error);
  }
});

router.post('/', async (req, res, next) => {
  try {
    const preorder = await Preorder.create({
      ...req.body,
      company_id: req.companyId
    });
    res.status(201).json(preorder);
  } catch (error) {
    next(error);
  }
});

module.exports = router;

const express = require('express');
const { Product } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();
router.use(authenticateToken);

// GET /products - Get all products
router.get('/', async (req, res, next) => {
  try {
    const products = await Product.findAll({
      where: { company_id: req.companyId },
      order: [['created_at', 'DESC']]
    });
    res.json(products);
  } catch (error) {
    next(error);
  }
});

// POST /products - Create product
router.post('/', async (req, res, next) => {
  try {
    const product = await Product.create({
      ...req.body,
      company_id: req.companyId
    });
    res.status(201).json(product);
  } catch (error) {
    next(error);
  }
});

// GET /products/:id - Get product by ID
router.get('/:id', async (req, res, next) => {
  try {
    const product = await Product.findOne({
      where: { id: req.params.id, company_id: req.companyId }
    });
    if (!product) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }
    res.json(product);
  } catch (error) {
    next(error);
  }
});

// PUT /products/:id - Update product
router.put('/:id', async (req, res, next) => {
  try {
    const product = await Product.findOne({
      where: { id: req.params.id, company_id: req.companyId }
    });
    if (!product) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }
    await product.update(req.body);
    res.json(product);
  } catch (error) {
    next(error);
  }
});

// DELETE /products/:id - Delete product
router.delete('/:id', async (req, res, next) => {
  try {
    const product = await Product.findOne({
      where: { id: req.params.id, company_id: req.companyId }
    });
    if (!product) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }
    await product.destroy();
    res.json({ message: 'Produto deletado com sucesso' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

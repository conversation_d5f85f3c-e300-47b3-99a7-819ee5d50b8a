const express = require('express');
const { TeamMember } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

router.get('/', async (req, res, next) => {
  try {
    const teamMembers = await TeamMember.findAll({
      where: { company_id: req.companyId },
      order: [['created_at', 'DESC']]
    });
    res.json(teamMembers);
  } catch (error) {
    next(error);
  }
});

router.post('/', async (req, res, next) => {
  try {
    const teamMember = await TeamMember.create({
      ...req.body,
      company_id: req.companyId
    });
    res.status(201).json(teamMember);
  } catch (error) {
    next(error);
  }
});

module.exports = router;

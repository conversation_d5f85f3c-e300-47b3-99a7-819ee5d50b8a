const Joi = require('joi');

const createLeadSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Nome deve ter pelo menos 1 caractere',
      'string.max': 'Nome deve ter no máximo 100 caracteres',
      'any.required': 'Nome é obrigatório'
    }),
  
  email: Joi.string()
    .email()
    .max(100)
    .optional()
    .allow(null, '')
    .messages({
      'string.email': 'Email deve ter um formato válido',
      'string.max': 'Email deve ter no máximo 100 caracteres'
    }),
  
  phone: Joi.string()
    .max(20)
    .optional()
    .allow(null, '')
    .messages({
      'string.max': 'Telefone deve ter no máximo 20 caracteres'
    }),
  
  social_media: Joi.string()
    .max(200)
    .optional()
    .allow(null, '')
    .messages({
      'string.max': 'Rede social deve ter no máximo 200 caracteres'
    }),
  
  source: Joi.string()
    .max(100)
    .optional()
    .allow(null, '')
    .messages({
      'string.max': 'Fonte deve ter no máximo 100 caracteres'
    }),
  
  status: Joi.string()
    .valid('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost')
    .default('new')
    .optional()
    .messages({
      'any.only': 'Status deve ser um dos valores válidos: new, contacted, qualified, proposal, negotiation, closed_won, closed_lost'
    })
});

const updateLeadSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Nome deve ter pelo menos 1 caractere',
      'string.max': 'Nome deve ter no máximo 100 caracteres'
    }),
  
  email: Joi.string()
    .email()
    .max(100)
    .optional()
    .allow(null, '')
    .messages({
      'string.email': 'Email deve ter um formato válido',
      'string.max': 'Email deve ter no máximo 100 caracteres'
    }),
  
  phone: Joi.string()
    .max(20)
    .optional()
    .allow(null, '')
    .messages({
      'string.max': 'Telefone deve ter no máximo 20 caracteres'
    }),
  
  social_media: Joi.string()
    .max(200)
    .optional()
    .allow(null, '')
    .messages({
      'string.max': 'Rede social deve ter no máximo 200 caracteres'
    }),
  
  source: Joi.string()
    .max(100)
    .optional()
    .allow(null, '')
    .messages({
      'string.max': 'Fonte deve ter no máximo 100 caracteres'
    }),
  
  status: Joi.string()
    .valid('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost')
    .optional()
    .messages({
      'any.only': 'Status deve ser um dos valores válidos: new, contacted, qualified, proposal, negotiation, closed_won, closed_lost'
    })
});

const leadIdSchema = Joi.object({
  lead_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID do lead deve ser um número',
      'number.integer': 'ID do lead deve ser um número inteiro',
      'number.positive': 'ID do lead deve ser um número positivo',
      'any.required': 'ID do lead é obrigatório'
    })
});

module.exports = {
  createLeadSchema,
  updateLeadSchema,
  leadIdSchema,
};

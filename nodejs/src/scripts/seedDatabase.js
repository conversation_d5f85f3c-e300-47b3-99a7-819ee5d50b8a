#!/usr/bin/env node

/**
 * Script para popular o banco de dados com dados iniciais
 */

require('dotenv').config();
const { sequelize } = require('../config/database');
const { hashPassword } = require('../utils/auth');
const logger = require('../utils/logger');

// Import models
const {
  Company,
  Admin,
  TeamMember,
  Product,
  Lead,
  Sale,
  Goal,
  Followup,
  Preorder
} = require('../models');

async function seedDatabase() {
  try {
    console.log('🌱 Iniciando seed do banco de dados...\n');

    // Sync database
    await sequelize.sync({ force: true });
    console.log('✅ Banco de dados sincronizado\n');

    // Create companies
    const companies = await Company.bulkCreate([
      { name: 'TechCorp Solutions' },
      { name: 'Digital Marketing Pro' },
      { name: 'E-commerce Plus' }
    ]);
    console.log(`✅ Criadas ${companies.length} empresas`);

    // Create admins
    const adminPassword = await hashPassword('admin123');
    const admins = await Admin.bulkCreate([
      {
        company_id: companies[0].id,
        username: 'admin_techcorp',
        password_hash: adminPassword,
        email: '<EMAIL>'
      },
      {
        company_id: companies[1].id,
        username: 'admin_marketing',
        password_hash: adminPassword,
        email: '<EMAIL>'
      },
      {
        company_id: companies[2].id,
        username: 'admin_ecommerce',
        password_hash: adminPassword,
        email: '<EMAIL>'
      }
    ]);
    console.log(`✅ Criados ${admins.length} administradores`);

    // Create team members
    const teamMembers = [];
    for (const company of companies) {
      const companyTeam = await TeamMember.bulkCreate([
        {
          company_id: company.id,
          name: 'João Silva',
          email: 'joao@' + company.name.toLowerCase().replace(/\s+/g, '') + '.com',
          phone: '(11) 99999-0001',
          position: 'Vendedor Senior'
        },
        {
          company_id: company.id,
          name: 'Maria Santos',
          email: 'maria@' + company.name.toLowerCase().replace(/\s+/g, '') + '.com',
          phone: '(11) 99999-0002',
          position: 'Gerente de Vendas'
        },
        {
          company_id: company.id,
          name: 'Pedro Costa',
          email: 'pedro@' + company.name.toLowerCase().replace(/\s+/g, '') + '.com',
          phone: '(11) 99999-0003',
          position: 'Analista de Marketing'
        }
      ]);
      teamMembers.push(...companyTeam);
    }
    console.log(`✅ Criados ${teamMembers.length} membros da equipe`);

    // Create products
    const products = [];
    for (const company of companies) {
      const companyProducts = await Product.bulkCreate([
        {
          company_id: company.id,
          name: 'Produto Premium',
          price: 299.99
        },
        {
          company_id: company.id,
          name: 'Produto Standard',
          price: 199.99
        },
        {
          company_id: company.id,
          name: 'Produto Basic',
          price: 99.99
        }
      ]);
      products.push(...companyProducts);
    }
    console.log(`✅ Criados ${products.length} produtos`);

    // Create leads
    const leads = [];
    const leadStatuses = ['new', 'contacted', 'qualified', 'proposal', 'negotiation'];
    const sources = ['Website', 'Facebook', 'Google Ads', 'Indicação', 'LinkedIn'];
    
    for (const company of companies) {
      const companyLeads = [];
      for (let i = 1; i <= 15; i++) {
        companyLeads.push({
          company_id: company.id,
          name: `Lead ${i} - ${company.name}`,
          email: `lead${i}@email.com`,
          phone: `(11) 9999-${String(i).padStart(4, '0')}`,
          social_media: `@lead${i}`,
          source: sources[Math.floor(Math.random() * sources.length)],
          status: leadStatuses[Math.floor(Math.random() * leadStatuses.length)]
        });
      }
      const createdLeads = await Lead.bulkCreate(companyLeads);
      leads.push(...createdLeads);
    }
    console.log(`✅ Criados ${leads.length} leads`);

    // Create sales
    const sales = [];
    for (const company of companies) {
      const companyLeads = leads.filter(lead => lead.company_id === company.id);
      const companyProducts = products.filter(product => product.company_id === company.id);
      
      const companySales = [];
      for (let i = 0; i < 10; i++) {
        const randomLead = companyLeads[Math.floor(Math.random() * companyLeads.length)];
        const randomProduct = companyProducts[Math.floor(Math.random() * companyProducts.length)];
        const saleDate = new Date();
        saleDate.setDate(saleDate.getDate() - Math.floor(Math.random() * 90));
        
        companySales.push({
          company_id: company.id,
          lead_id: randomLead.id,
          product_id: randomProduct.id,
          amount: randomProduct.price,
          sale_date: saleDate.toISOString().split('T')[0],
          notes: `Venda realizada para ${randomLead.name}`
        });
      }
      const createdSales = await Sale.bulkCreate(companySales);
      sales.push(...createdSales);
    }
    console.log(`✅ Criadas ${sales.length} vendas`);

    console.log('\n🎉 Seed do banco de dados concluído com sucesso!');
    console.log('\n📋 Resumo dos dados criados:');
    console.log(`   • ${companies.length} empresas`);
    console.log(`   • ${admins.length} administradores`);
    console.log(`   • ${teamMembers.length} membros da equipe`);
    console.log(`   • ${products.length} produtos`);
    console.log(`   • ${leads.length} leads`);
    console.log(`   • ${sales.length} vendas`);

    console.log('\n🔐 Credenciais de acesso:');
    for (const admin of admins) {
      const company = companies.find(c => c.id === admin.company_id);
      console.log(`   • ${admin.username} (${company.name}) - senha: admin123`);
    }

    console.log('\n✨ Dados de seed criados com sucesso!');

  } catch (error) {
    console.error('❌ Erro durante o seed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run seed if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };

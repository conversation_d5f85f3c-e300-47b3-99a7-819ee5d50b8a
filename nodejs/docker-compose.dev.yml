version: '3.8'

services:
  # PostgreSQL Database for development
  postgres:
    image: postgres:15-alpine
    container_name: multitenancy-postgres-dev
    environment:
      POSTGRES_DB: multitenancy_hub
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: devpass
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - multitenancy-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev -d multitenancy_hub"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Node.js API for development
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: multitenancy-api-dev
    environment:
      NODE_ENV: development
      PORT: 8000
      DATABASE_URL: **************************************/multitenancy_hub
      JWT_SECRET: dev-jwt-secret-key
      JWT_EXPIRES_IN: 24h
      FRONTEND_URL: http://localhost:3000
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    networks:
      - multitenancy-dev-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    command: npm run dev

volumes:
  postgres_dev_data:
    driver: local

networks:
  multitenancy-dev-network:
    driver: bridge
